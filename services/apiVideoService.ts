import { MergedSubtitle, VideoProcessingOptions, VideoProcessingProgress, ProcessedVideoResult, SubtitleStyle } from '../types';
import { generateSRTContent } from '../utils/subtitleUtils';

export interface VideoProcessingRequest {
  videoFile: File;
  srtContent: string;
  srtOptions: {
    embedOriginalSubtitles: boolean;
    embedTranslatedSubtitles: boolean;
    subtitleStyle: SubtitleStyle;
    outputFormat: 'mp4' | 'webm' | 'avi';
  };
}

export interface TaskResponse {
  taskId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  message?: string;
}

export interface TaskStatusResponse {
  taskId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  message: string;
  error?: string;
  downloadUrl?: string;
  filename?: string;
  size?: number;
}

class ApiVideoService {
  private baseUrl = 'http://localhost:8888/api/capability';
  private progressCallback: ((progress: VideoProcessingProgress) => void) | null = null;
  private pollingInterval: number = 2000; // 2 seconds
  private maxPollingAttempts: number = 300; // 10 minutes max (300 * 2 seconds)

  setProgressCallback(callback: (progress: VideoProcessingProgress) => void) {
    this.progressCallback = callback;
  }

  private updateProgress(stage: VideoProcessingProgress['stage'], progress: number, message: string, error?: string) {
    if (this.progressCallback) {
      this.progressCallback({ stage, progress, message, error });
    }
  }

  async processVideoWithSubtitles(
    videoFile: File,
    subtitles: MergedSubtitle[],
    options: VideoProcessingOptions
  ): Promise<ProcessedVideoResult> {
    try {
      this.updateProgress('initializing', 5, 'Preparing video processing request...');

      // Generate SRT content for both original and translated subtitles
      const originalSrtLines = subtitles
        .filter(sub => sub.originalText.trim())
        .map(sub => ({
          id: sub.id,
          startTime: sub.startTime,
          endTime: sub.endTime,
          text: sub.originalText,
        }));

      const translatedSrtLines = subtitles
        .filter(sub => sub.translatedText.trim())
        .map(sub => ({
          id: sub.id,
          startTime: sub.startTime,
          endTime: sub.endTime,
          text: sub.translatedText,
        }));

      // Generate combined SRT content
      let srtContent = '';
      if (options.embedOriginalSubtitles && originalSrtLines.length > 0) {
        srtContent += generateSRTContent(originalSrtLines);
      }
      if (options.embedTranslatedSubtitles && translatedSrtLines.length > 0) {
        if (srtContent) srtContent += '\n\n';
        srtContent += generateSRTContent(translatedSrtLines);
      }

      if (!srtContent.trim()) {
        throw new Error('No subtitle content to embed');
      }

      this.updateProgress('uploading', 10, 'Uploading video and subtitle data...');

      // Prepare form data
      const formData = new FormData();
      formData.append('videoFile', videoFile);
      formData.append('srtContent', srtContent);
      formData.append('srtOptions', JSON.stringify({
        embedOriginalSubtitles: options.embedOriginalSubtitles,
        embedTranslatedSubtitles: options.embedTranslatedSubtitles,
        subtitleStyle: options.subtitleStyle,
        outputFormat: options.outputFormat,
      }));

      // Submit processing request
      console.log('Submitting video processing request to:', `${this.baseUrl}/executeAction`);
      const response = await fetch(`${this.baseUrl}/executeAction`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API request failed:', response.status, errorText);
        throw new Error(`Failed to submit video processing request: ${response.status} ${errorText}`);
      }

      const taskResponse: TaskResponse = await response.json();
      this.updateProgress('processing_video', 15, `Task submitted with ID: ${taskResponse.taskId}`);

      // Poll for task completion
      const result = await this.pollTaskStatus(taskResponse.taskId);
      return result;

    } catch (error) {
      console.error('API video processing error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred during video processing';
      this.updateProgress('error', 0, 'Video processing failed', errorMessage);
      throw new Error(`Video processing failed: ${errorMessage}`);
    }
  }

  private async pollTaskStatus(taskId: string): Promise<ProcessedVideoResult> {
    let attempts = 0;

    while (attempts < this.maxPollingAttempts) {
      try {
        this.updateProgress('processing_video', 20 + (attempts / this.maxPollingAttempts) * 70,
          `Checking task status... (${attempts + 1}/${this.maxPollingAttempts})`);

        const response = await fetch(`${this.baseUrl}/subtitleTask?taskId=${taskId}`);

        if (!response.ok) {
          throw new Error(`Failed to check task status: ${response.status}`);
        }

        const status: TaskStatusResponse = await response.json();

        // Update progress based on task status
        this.updateProgress('processing_video', Math.max(20, status.progress), status.message, status.error);

        switch (status.status) {
          case 'completed':
            if (!status.downloadUrl) {
              throw new Error('Task completed but no download URL provided');
            }
            return await this.downloadProcessedVideo(status);

          case 'failed':
            throw new Error(status.error || 'Video processing failed on server');

          case 'processing':
          case 'pending':
            // Continue polling
            break;

          default:
            throw new Error(`Unknown task status: ${status.status}`);
        }

        attempts++;

        if (attempts < this.maxPollingAttempts) {
          await new Promise(resolve => setTimeout(resolve, this.pollingInterval));
        }

      } catch (error) {
        console.error('Error polling task status:', error);
        if (attempts >= this.maxPollingAttempts - 1) {
          throw error;
        }
        attempts++;
        await new Promise(resolve => setTimeout(resolve, this.pollingInterval));
      }
    }

    throw new Error('Video processing timed out. Please try again.');
  }

  private async downloadProcessedVideo(status: TaskStatusResponse): Promise<ProcessedVideoResult> {
    try {
      this.updateProgress('downloading', 95, 'Downloading processed video...');

      const response = await fetch(status.downloadUrl!);

      if (!response.ok) {
        throw new Error(`Failed to download processed video: ${response.status}`);
      }

      const videoBlob = await response.blob();

      this.updateProgress('completed', 100, 'Video processing completed successfully');

      return {
        videoBlob,
        filename: status.filename || 'processed_video.mp4',
        duration: 0, // Duration not provided by API
        size: status.size || videoBlob.size
      };

    } catch (error) {
      console.error('Error downloading processed video:', error);
      throw new Error(`Failed to download processed video: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Method to cancel a task (if the API supports it)
  async cancelTask(taskId: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/cancelTask`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ taskId }),
      });

      if (!response.ok) {
        console.warn(`Failed to cancel task ${taskId}: ${response.status}`);
      }
    } catch (error) {
      console.warn('Error canceling task:', error);
    }
  }

  // Method to get task history (if the API supports it)
  async getTaskHistory(): Promise<TaskStatusResponse[]> {
    try {
      const response = await fetch(`${this.baseUrl}/taskHistory`);

      if (!response.ok) {
        throw new Error(`Failed to get task history: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error getting task history:', error);
      return [];
    }
  }
}

export const apiVideoService = new ApiVideoService();
